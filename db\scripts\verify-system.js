#!/usr/bin/env node

/**
 * 验证系统功能 - 测试数据库重构后的系统完整性
 */

const sqlite3 = require('sqlite3');
const { open } = require('sqlite');

class SystemVerifier {
  constructor() {
    this.dbPath = './db/药店管理系统.db';
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
  }

  async runTest(name, testFn) {
    try {
      console.log(`🧪 测试: ${name}`);
      await testFn();
      console.log(`  ✅ 通过`);
      this.passed++;
    } catch (error) {
      console.log(`  ❌ 失败: ${error.message}`);
      this.failed++;
    }
  }

  async verifyDatabaseConnection() {
    await this.runTest('数据库连接', async () => {
      const db = await open({
        filename: this.dbPath,
        driver: sqlite3.Database
      });
      await db.close();
    });
  }

  async verifyTableStructure() {
    await this.runTest('表结构验证', async () => {
      const db = await open({
        filename: this.dbPath,
        driver: sqlite3.Database
      });

      const expectedTables = [
        '系统设置', '药品分类', '药品信息', '客户信息',
        '销售订单', '订单明细', '库存记录', '供应商'
      ];

      const tables = await db.all("SELECT name FROM sqlite_master WHERE type='table'");
      const tableNames = tables.map(t => t.name).filter(name => name !== 'sqlite_sequence');

      for (const expectedTable of expectedTables) {
        if (!tableNames.includes(expectedTable)) {
          throw new Error(`缺少表: ${expectedTable}`);
        }
      }

      await db.close();
    });
  }

  async verifyDataIntegrity() {
    await this.runTest('数据完整性验证', async () => {
      const db = await open({
        filename: this.dbPath,
        driver: sqlite3.Database
      });

      // 检查系统设置
      const settings = await db.get('SELECT COUNT(*) as count FROM 系统设置');
      if (settings.count < 8) {
        throw new Error(`系统设置数据不足: ${settings.count}`);
      }

      // 检查药品分类
      const categories = await db.get('SELECT COUNT(*) as count FROM 药品分类');
      if (categories.count < 5) {
        throw new Error(`药品分类数据不足: ${categories.count}`);
      }

      // 检查药品信息
      const products = await db.get('SELECT COUNT(*) as count FROM 药品信息');
      if (products.count < 5) {
        throw new Error(`药品信息数据不足: ${products.count}`);
      }

      await db.close();
    });
  }

  async verifyForeignKeys() {
    await this.runTest('外键关系验证', async () => {
      const db = await open({
        filename: this.dbPath,
        driver: sqlite3.Database
      });

      // 检查药品-分类关系
      const productCategories = await db.all(`
        SELECT p.名称, c.名称 as 分类名称
        FROM 药品信息 p
        LEFT JOIN 药品分类 c ON p.分类编号 = c.编号
        WHERE c.编号 IS NULL
      `);

      if (productCategories.length > 0) {
        throw new Error(`发现无效的药品分类关系: ${productCategories.length} 条`);
      }

      // 检查订单-客户关系
      const orderCustomers = await db.all(`
        SELECT o.订单编号, c.姓名
        FROM 销售订单 o
        LEFT JOIN 客户信息 c ON o.客户编号 = c.编号
        WHERE o.客户编号 IS NOT NULL AND c.编号 IS NULL
      `);

      if (orderCustomers.length > 0) {
        throw new Error(`发现无效的订单客户关系: ${orderCustomers.length} 条`);
      }

      await db.close();
    });
  }

  async verifyQueryPerformance() {
    await this.runTest('查询性能验证', async () => {
      const db = await open({
        filename: this.dbPath,
        driver: sqlite3.Database
      });

      const startTime = Date.now();

      // 执行复杂查询
      await db.all(`
        SELECT 
          p.编号,
          p.名称,
          p.售价,
          c.名称 as 分类名称,
          p.库存数量
        FROM 药品信息 p
        LEFT JOIN 药品分类 c ON p.分类编号 = c.编号
        WHERE p.状态 = 'active'
        ORDER BY p.编号 DESC
      `);

      const endTime = Date.now();
      const queryTime = endTime - startTime;

      if (queryTime > 1000) {
        throw new Error(`查询时间过长: ${queryTime}ms`);
      }

      await db.close();
    });
  }

  async verifyDataConsistency() {
    await this.runTest('数据一致性验证', async () => {
      const db = await open({
        filename: this.dbPath,
        driver: sqlite3.Database
      });

      // 检查订单金额一致性
      const orderTotals = await db.all(`
        SELECT 
          o.编号,
          o.总金额,
          SUM(od.小计) as 计算总额
        FROM 销售订单 o
        LEFT JOIN 订单明细 od ON o.编号 = od.订单编号
        GROUP BY o.编号
        HAVING ABS(o.总金额 - COALESCE(计算总额, 0)) > 0.01
      `);

      if (orderTotals.length > 0) {
        throw new Error(`发现订单金额不一致: ${orderTotals.length} 条`);
      }

      await db.close();
    });
  }

  async verifyIndexes() {
    await this.runTest('索引验证', async () => {
      const db = await open({
        filename: this.dbPath,
        driver: sqlite3.Database
      });

      const indexes = await db.all("SELECT name FROM sqlite_master WHERE type='index'");
      const indexNames = indexes.map(i => i.name);

      const expectedIndexes = [
        'idx_药品信息_条形码',
        'idx_药品信息_追溯码',
        'idx_销售订单_订单编号',
        'idx_订单明细_订单编号'
      ];

      for (const expectedIndex of expectedIndexes) {
        if (!indexNames.includes(expectedIndex)) {
          throw new Error(`缺少索引: ${expectedIndex}`);
        }
      }

      await db.close();
    });
  }

  async generateVerificationReport() {
    const report = {
      timestamp: new Date().toISOString(),
      database: this.dbPath,
      tests: {
        total: this.passed + this.failed,
        passed: this.passed,
        failed: this.failed,
        success_rate: ((this.passed / (this.passed + this.failed)) * 100).toFixed(2) + '%'
      },
      status: this.failed === 0 ? 'PASS' : 'FAIL',
      recommendations: []
    };

    if (this.failed > 0) {
      report.recommendations.push('请检查失败的测试项目并修复相关问题');
      report.recommendations.push('建议重新运行验证脚本确认修复结果');
    } else {
      report.recommendations.push('系统验证通过，可以正常使用');
      report.recommendations.push('建议定期运行此验证脚本确保系统稳定性');
    }

    const reportPath = './db/system-verification-report.json';
    const fs = require('fs');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    return report;
  }

  async run() {
    console.log('🔍 开始系统验证...\n');

    await this.verifyDatabaseConnection();
    await this.verifyTableStructure();
    await this.verifyDataIntegrity();
    await this.verifyForeignKeys();
    await this.verifyQueryPerformance();
    await this.verifyDataConsistency();
    await this.verifyIndexes();

    console.log('\n📊 生成验证报告...');
    const report = await this.generateVerificationReport();

    console.log('\n🎯 验证结果:');
    console.log(`  总测试数: ${report.tests.total}`);
    console.log(`  通过: ${report.tests.passed}`);
    console.log(`  失败: ${report.tests.failed}`);
    console.log(`  成功率: ${report.tests.success_rate}`);
    console.log(`  状态: ${report.status}`);

    if (report.status === 'PASS') {
      console.log('\n🎉 系统验证通过！数据库重构成功完成！');
    } else {
      console.log('\n⚠️ 系统验证发现问题，请检查并修复');
    }

    console.log(`\n📄 详细报告: ./db/system-verification-report.json`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const verifier = new SystemVerifier();
  verifier.run();
}

module.exports = SystemVerifier;
