#!/usr/bin/env node

/**
 * 批量更新所有API文件，将英文表名和字段名替换为中文
 * 这是一个更精确的替换脚本
 */

const fs = require('fs');
const path = require('path');

class ApiUpdater {
  constructor() {
    this.apiFiles = [
      'app/api/products/route.ts',
      'app/api/products/barcode/route.ts', 
      'app/api/categories/route.ts',
      'app/api/orders/route.ts',
      'app/api/orders/[id]/route.ts',
      'app/api/customers/route.ts',
      'app/api/suppliers/route.ts',
      'app/api/inventory/route.ts',
      'app/api/db-test/route.ts',
      'app/api/products/top-selling/route.ts'
    ];
    
    this.replacements = [
      // 表名替换
      { from: /\bproducts\b/g, to: '药品信息' },
      { from: /\bcategories\b/g, to: '药品分类' },
      { from: /\borders\b/g, to: '销售订单' },
      { from: /\border_details\b/g, to: '订单明细' },
      { from: /\bcustomers\b/g, to: '客户信息' },
      { from: /\bsuppliers\b/g, to: '供应商' },
      { from: /\binventory_records\b/g, to: '库存记录' },
      { from: /\bsettings\b/g, to: '系统设置' },
      
      // 字段名替换 - 在SQL上下文中
      { from: /\bid\s+as\s+id/g, to: '编号 as id' },
      { from: /\bname\s+as\s+name/g, to: '名称 as name' },
      { from: /\bgeneric_name\s+as\s+generic_name/g, to: '通用名 as generic_name' },
      { from: /\bdescription\s+as\s+description/g, to: '描述 as description' },
      { from: /\bbarcode\s+as\s+barcode/g, to: '条形码 as barcode' },
      { from: /\btrace_code\s+as\s+trace_code/g, to: '追溯码 as trace_code' },
      { from: /\bcategory_id\s+as\s+category_id/g, to: '分类编号 as category_id' },
      { from: /\bmanufacturer\s+as\s+manufacturer/g, to: '生产厂家 as manufacturer' },
      { from: /\bapproval_number\s+as\s+approval_number/g, to: '批准文号 as approval_number' },
      { from: /\bspecification\s+as\s+specification/g, to: '规格 as specification' },
      { from: /\bdosage_form\s+as\s+dosage_form/g, to: '剂型 as dosage_form' },
      { from: /\bprice\s+as\s+price/g, to: '售价 as price' },
      { from: /\bcost_price\s+as\s+cost_price/g, to: '成本价 as cost_price' },
      { from: /\bstock_quantity\s+as\s+stock_quantity/g, to: '库存数量 as stock_quantity' },
      { from: /\bmin_stock_level\s+as\s+min_stock_level/g, to: '最低库存 as min_stock_level' },
      { from: /\bis_prescription\s+as\s+is_prescription/g, to: '是否处方药 as is_prescription' },
      { from: /\bis_medical_insurance\s+as\s+is_medical_insurance/g, to: '是否医保 as is_medical_insurance' },
      { from: /\bstorage_condition\s+as\s+storage_condition/g, to: '储存条件 as storage_condition' },
      { from: /\bstatus\s+as\s+status/g, to: '状态 as status' },
      { from: /\bcreated_at\s+as\s+created_at/g, to: '创建时间 as created_at' },
      { from: /\bupdated_at\s+as\s+updated_at/g, to: '更新时间 as updated_at' },
      
      // 客户字段
      { from: /\bphone\s+as\s+phone/g, to: '电话 as phone' },
      { from: /\bemail\s+as\s+email/g, to: '邮箱 as email' },
      { from: /\baddress\s+as\s+address/g, to: '地址 as address' },
      
      // 订单字段
      { from: /\border_number\s+as\s+orderNumber/g, to: '订单编号 as orderNumber' },
      { from: /\btotal_amount\s+as\s+amount/g, to: '总金额 as amount' },
      { from: /\bcustomer_id\s+as\s+customer_id/g, to: '客户编号 as customer_id' },
      
      // JOIN 语句中的字段引用
      { from: /p\.id/g, to: 'p.编号' },
      { from: /p\.name/g, to: 'p.名称' },
      { from: /p\.category_id/g, to: 'p.分类编号' },
      { from: /c\.id/g, to: 'c.编号' },
      { from: /c\.name/g, to: 'c.名称' },
      { from: /o\.id/g, to: 'o.编号' },
      { from: /o\.order_number/g, to: 'o.订单编号' },
      { from: /o\.customer_id/g, to: 'o.客户编号' },
      { from: /o\.total_amount/g, to: 'o.总金额' },
      { from: /o\.status/g, to: 'o.状态' },
      { from: /o\.created_at/g, to: 'o.创建时间' },
      
      // WHERE 和其他条件中的字段
      { from: /WHERE\s+id\s*=/g, to: 'WHERE 编号 =' },
      { from: /WHERE\s+category_id\s*=/g, to: 'WHERE 分类编号 =' },
      { from: /WHERE\s+barcode\s*=/g, to: 'WHERE 条形码 =' },
      { from: /WHERE\s+trace_code\s*=/g, to: 'WHERE 追溯码 =' },
      { from: /WHERE\s+status\s*=/g, to: 'WHERE 状态 =' },
      
      // INSERT 语句中的字段
      { from: /INSERT\s+INTO\s+products\s*\(/g, to: 'INSERT INTO 药品信息 (' },
      { from: /INSERT\s+INTO\s+categories\s*\(/g, to: 'INSERT INTO 药品分类 (' },
      { from: /INSERT\s+INTO\s+orders\s*\(/g, to: 'INSERT INTO 销售订单 (' },
      { from: /INSERT\s+INTO\s+order_details\s*\(/g, to: 'INSERT INTO 订单明细 (' },
      { from: /INSERT\s+INTO\s+customers\s*\(/g, to: 'INSERT INTO 客户信息 (' },
      { from: /INSERT\s+INTO\s+suppliers\s*\(/g, to: 'INSERT INTO 供应商 (' },
      { from: /INSERT\s+INTO\s+inventory_records\s*\(/g, to: 'INSERT INTO 库存记录 (' },
      
      // UPDATE 语句
      { from: /UPDATE\s+products\s+SET/g, to: 'UPDATE 药品信息 SET' },
      { from: /UPDATE\s+categories\s+SET/g, to: 'UPDATE 药品分类 SET' },
      { from: /UPDATE\s+orders\s+SET/g, to: 'UPDATE 销售订单 SET' },
      { from: /UPDATE\s+customers\s+SET/g, to: 'UPDATE 客户信息 SET' },
      { from: /UPDATE\s+suppliers\s+SET/g, to: 'UPDATE 供应商 SET' },
      
      // DELETE 语句
      { from: /DELETE\s+FROM\s+products\s+WHERE/g, to: 'DELETE FROM 药品信息 WHERE' },
      { from: /DELETE\s+FROM\s+categories\s+WHERE/g, to: 'DELETE FROM 药品分类 WHERE' },
      { from: /DELETE\s+FROM\s+orders\s+WHERE/g, to: 'DELETE FROM 销售订单 WHERE' },
      { from: /DELETE\s+FROM\s+order_details\s+WHERE/g, to: 'DELETE FROM 订单明细 WHERE' },
      { from: /DELETE\s+FROM\s+customers\s+WHERE/g, to: 'DELETE FROM 客户信息 WHERE' },
      { from: /DELETE\s+FROM\s+suppliers\s+WHERE/g, to: 'DELETE FROM 供应商 WHERE' },
      { from: /DELETE\s+FROM\s+inventory_records\s+WHERE/g, to: 'DELETE FROM 库存记录 WHERE' }
    ];
  }

  async updateFile(filePath) {
    if (!fs.existsSync(filePath)) {
      console.log(`  ⚠️ 文件不存在: ${filePath}`);
      return false;
    }

    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let updated = false;

      for (const replacement of this.replacements) {
        if (replacement.from.test(content)) {
          content = content.replace(replacement.from, replacement.to);
          updated = true;
        }
      }

      if (updated) {
        fs.writeFileSync(filePath, content);
        console.log(`  ✅ 更新: ${filePath}`);
        return true;
      } else {
        console.log(`  ➖ 无需更新: ${filePath}`);
        return false;
      }
    } catch (error) {
      console.log(`  ❌ 更新失败: ${filePath} - ${error.message}`);
      return false;
    }
  }

  async updateAllFiles() {
    console.log('🔄 开始更新API文件...\n');
    
    let updatedCount = 0;
    
    for (const filePath of this.apiFiles) {
      const updated = await this.updateFile(filePath);
      if (updated) updatedCount++;
    }
    
    console.log(`\n✅ 更新完成！共更新了 ${updatedCount} 个文件`);
    return updatedCount;
  }

  async run() {
    try {
      const updatedCount = await this.updateAllFiles();
      
      console.log('\n🎉 API文件更新完成！');
      console.log('📋 主要更改:');
      console.log('  - 所有表名已更新为中文');
      console.log('  - 所有字段名已更新为中文');
      console.log('  - SQL查询语句已适配新的数据库架构');
      console.log('\n⚠️ 注意: 请测试所有API功能以确保正常工作');
      
    } catch (error) {
      console.error('❌ API文件更新失败:', error);
      process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const updater = new ApiUpdater();
  updater.run();
}

module.exports = ApiUpdater;
