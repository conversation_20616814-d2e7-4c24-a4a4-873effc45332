-- 药店零售管理系统中文数据库架构
-- 创建时间: 2025-06-08
-- 版本: 3.0
-- 说明: 完整的中文数据库架构，替换现有英文数据库

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 系统设置表
DROP TABLE IF EXISTS 系统设置;
CREATE TABLE 系统设置 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    设置名称 TEXT NOT NULL UNIQUE,
    设置值 TEXT,
    描述 TEXT,
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 药品分类表
DROP TABLE IF EXISTS 药品分类;
CREATE TABLE 药品分类 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    名称 TEXT NOT NULL,
    描述 TEXT,
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 供应商表
DROP TABLE IF EXISTS 供应商;
CREATE TABLE 供应商 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    名称 TEXT NOT NULL,
    联系人 TEXT,
    电话 TEXT,
    邮箱 TEXT,
    地址 TEXT,
    税号 TEXT,
    银行账户 TEXT,
    开户行 TEXT,
    状态 TEXT DEFAULT 'active' CHECK (状态 IN ('active', 'inactive')),
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 药品信息表
DROP TABLE IF EXISTS 药品信息;
CREATE TABLE 药品信息 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    名称 TEXT NOT NULL,
    通用名 TEXT,
    描述 TEXT,
    条形码 TEXT UNIQUE,
    追溯码 TEXT,
    分类编号 INTEGER,
    生产厂家 TEXT,
    批准文号 TEXT,
    规格 TEXT,
    剂型 TEXT,
    售价 REAL NOT NULL CHECK (售价 >= 0),
    成本价 REAL CHECK (成本价 >= 0),
    库存数量 INTEGER DEFAULT 0 CHECK (库存数量 >= 0),
    最低库存 INTEGER DEFAULT 0 CHECK (最低库存 >= 0),
    是否处方药 INTEGER DEFAULT 0 CHECK (是否处方药 IN (0, 1)),
    是否医保 INTEGER DEFAULT 0 CHECK (是否医保 IN (0, 1)),
    储存条件 TEXT,
    状态 TEXT DEFAULT 'active' CHECK (状态 IN ('active', 'inactive')),
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (分类编号) REFERENCES 药品分类(编号)
);

-- 客户信息表
DROP TABLE IF EXISTS 客户信息;
CREATE TABLE 客户信息 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    姓名 TEXT NOT NULL,
    电话 TEXT,
    邮箱 TEXT,
    地址 TEXT,
    身份证号 TEXT,
    医保卡号 TEXT,
    会员等级 TEXT DEFAULT 'regular' CHECK (会员等级 IN ('regular', 'vip', 'gold')),
    积分 INTEGER DEFAULT 0 CHECK (积分 >= 0),
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 销售订单表
DROP TABLE IF EXISTS 销售订单;
CREATE TABLE 销售订单 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    订单编号 TEXT NOT NULL UNIQUE,
    客户编号 INTEGER,
    总金额 REAL NOT NULL CHECK (总金额 >= 0),
    折扣金额 REAL DEFAULT 0 CHECK (折扣金额 >= 0),
    应付金额 REAL NOT NULL CHECK (应付金额 >= 0),
    实付金额 REAL DEFAULT 0 CHECK (实付金额 >= 0),
    找零金额 REAL DEFAULT 0 CHECK (找零金额 >= 0),
    支付方式 TEXT DEFAULT 'cash' CHECK (支付方式 IN ('cash', 'card', 'alipay', 'wechat', 'medical_card')),
    状态 TEXT DEFAULT 'pending' CHECK (状态 IN ('pending', 'completed', 'cancelled', 'refunded')),
    备注 TEXT,
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (客户编号) REFERENCES 客户信息(编号)
);

-- 订单明细表
DROP TABLE IF EXISTS 订单明细;
CREATE TABLE 订单明细 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    订单编号 INTEGER NOT NULL,
    药品编号 INTEGER NOT NULL,
    数量 INTEGER NOT NULL CHECK (数量 > 0),
    单价 REAL NOT NULL CHECK (单价 >= 0),
    小计 REAL NOT NULL CHECK (小计 >= 0),
    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (订单编号) REFERENCES 销售订单(编号) ON DELETE CASCADE,
    FOREIGN KEY (药品编号) REFERENCES 药品信息(编号)
);

-- 库存记录表
DROP TABLE IF EXISTS 库存记录;
CREATE TABLE 库存记录 (
    编号 INTEGER PRIMARY KEY AUTOINCREMENT,
    药品编号 INTEGER NOT NULL,
    变动数量 INTEGER NOT NULL,
    变动类型 TEXT NOT NULL CHECK (变动类型 IN ('in', 'out', 'adjust')),
    参考单号 TEXT,
    供应商编号 INTEGER,
    备注 TEXT,
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
    FOREIGN KEY (供应商编号) REFERENCES 供应商(编号)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_药品信息_条形码 ON 药品信息(条形码);
CREATE INDEX IF NOT EXISTS idx_药品信息_追溯码 ON 药品信息(追溯码);
CREATE INDEX IF NOT EXISTS idx_药品信息_分类编号 ON 药品信息(分类编号);
CREATE INDEX IF NOT EXISTS idx_药品信息_状态 ON 药品信息(状态);
CREATE INDEX IF NOT EXISTS idx_销售订单_订单编号 ON 销售订单(订单编号);
CREATE INDEX IF NOT EXISTS idx_销售订单_客户编号 ON 销售订单(客户编号);
CREATE INDEX IF NOT EXISTS idx_销售订单_状态 ON 销售订单(状态);
CREATE INDEX IF NOT EXISTS idx_销售订单_创建时间 ON 销售订单(创建时间);
CREATE INDEX IF NOT EXISTS idx_订单明细_订单编号 ON 订单明细(订单编号);
CREATE INDEX IF NOT EXISTS idx_订单明细_药品编号 ON 订单明细(药品编号);
CREATE INDEX IF NOT EXISTS idx_库存记录_药品编号 ON 库存记录(药品编号);
CREATE INDEX IF NOT EXISTS idx_库存记录_变动类型 ON 库存记录(变动类型);
CREATE INDEX IF NOT EXISTS idx_库存记录_创建时间 ON 库存记录(创建时间);

-- 插入默认系统设置
INSERT OR REPLACE INTO 系统设置 (设置名称, 设置值, 描述) VALUES
('药店名称', '药店零售管理系统', '药店名称设置'),
('订单编号位数', '4', '订单编号日期后的序号位数'),
('默认支付方式', 'cash', '默认支付方式'),
('是否启用会员功能', '1', '是否启用会员积分功能'),
('码上放心AppKey', '', '码上放心开放平台AppKey'),
('码上放心AppSecret', '', '码上放心开放平台AppSecret'),
('码上放心企业ID', '24123445', '码上放心开放平台企业ID'),
('小票打印纸张大小', '80mm', '小票打印纸张大小(58mm/80mm/112mm)');

-- 插入默认药品分类
INSERT OR REPLACE INTO 药品分类 (编号, 名称, 描述) VALUES
(1, '抗感染药', '用于治疗各种感染性疾病的药物'),
(2, '解热镇痛药', '用于退热和镇痛的药物'),
(3, '感冒药', '用于治疗感冒症状的药物'),
(4, '中成药', '传统中药制剂'),
(5, '维生素类', '各种维生素补充剂'),
(6, '消化系统药', '用于治疗消化系统疾病的药物'),
(7, '心血管药', '用于治疗心血管疾病的药物'),
(8, '外用药', '外用药膏、喷剂等'),
(9, '保健品', '营养保健类产品'),
(10, '医疗器械', '血压计、血糖仪等医疗器械');

-- 插入示例药品数据
INSERT OR REPLACE INTO 药品信息 (编号, 名称, 通用名, 描述, 条形码, 分类编号, 生产厂家, 规格, 剂型, 售价, 成本价, 库存数量, 最低库存, 储存条件, 状态) VALUES
(1, '阿莫西林胶囊', '阿莫西林胶囊', '青霉素类抗生素，用于敏感菌所致的各种感染', '6901234567890', 1, '哈药集团制药总厂', '0.25g*24粒/盒', '胶囊剂', 36.8, 28.5, 120, 10, '密闭，在干燥处保存', 'active'),
(2, '布洛芬片', '布洛芬片', '解热镇痛抗炎药，用于缓解轻至中度疼痛', '6901234567891', 2, '上海信谊药厂有限公司', '0.1g*24片/盒', '片剂', 18.5, 12.8, 85, 10, '密封保存', 'active'),
(3, '感冒灵颗粒', '感冒灵颗粒', '解表清热，用于感冒引起的头痛发热', '6901234567892', 3, '哈药集团制药总厂', '10g*9袋/盒', '颗粒剂', 25.6, 18.2, 64, 10, '密封，防潮', 'active'),
(4, '板蓝根颗粒', '板蓝根颗粒', '清热解毒，用于病毒性感冒', '6901234567893', 4, '广州白云山和记黄埔中药有限公司', '10g*20袋/盒', '颗粒剂', 32.8, 24.5, 76, 10, '密封，防潮', 'active'),
(5, '维生素C片', '维生素C片', '维生素类药，用于预防坏血病', '6901234567894', 5, '华北制药股份有限公司', '0.1g*100片/瓶', '片剂', 12.5, 8.6, 180, 20, '遮光，密封保存', 'active');

-- 插入示例客户数据
INSERT OR REPLACE INTO 客户信息 (编号, 姓名, 电话, 地址, 会员等级, 积分) VALUES
(1, '张三', '13800138001', '北京市朝阳区', 'regular', 0),
(2, '李四', '13800138002', '北京市海淀区', 'vip', 150),
(3, '王五', '13800138003', '北京市西城区', 'regular', 50),
(4, '赵六', '13800138004', '北京市东城区', 'gold', 300),
(5, '钱七', '13800138005', '北京市丰台区', 'regular', 25);
