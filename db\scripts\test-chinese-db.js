#!/usr/bin/env node

/**
 * 测试中文数据库连接和数据查询
 */

const sqlite3 = require('sqlite3');
const { open } = require('sqlite');

async function testChineseDatabase() {
  console.log('🧪 测试中文数据库连接和查询...\n');
  
  try {
    // 连接数据库
    const db = await open({
      filename: './db/药店管理系统.db',
      driver: sqlite3.Database
    });
    
    console.log('✅ 数据库连接成功');
    
    // 查询所有表
    const tables = await db.all("SELECT name FROM sqlite_master WHERE type='table'");
    console.log('\n📋 数据库中的表:');
    tables.forEach(table => {
      console.log(`  - ${table.name}`);
    });
    
    // 测试各表的数据
    const testTables = [
      { name: '系统设置', desc: '系统设置' },
      { name: '药品分类', desc: '药品分类' },
      { name: '药品信息', desc: '药品信息' },
      { name: '客户信息', desc: '客户信息' },
      { name: '销售订单', desc: '销售订单' },
      { name: '订单明细', desc: '订单明细' },
      { name: '库存记录', desc: '库存记录' },
      { name: '供应商', desc: '供应商' }
    ];
    
    console.log('\n📊 各表数据统计:');
    for (const table of testTables) {
      try {
        const result = await db.get(`SELECT COUNT(*) as count FROM ${table.name}`);
        console.log(`  ${table.desc}: ${result.count} 条记录`);
      } catch (error) {
        console.log(`  ${table.desc}: 查询失败 - ${error.message}`);
      }
    }
    
    // 测试药品信息查询
    console.log('\n💊 药品信息示例:');
    try {
      const products = await db.all(`
        SELECT 
          p.编号 as id,
          p.名称 as name,
          p.售价 as price,
          c.名称 as category_name
        FROM 药品信息 p
        LEFT JOIN 药品分类 c ON p.分类编号 = c.编号
        LIMIT 3
      `);
      
      products.forEach(product => {
        console.log(`  - ${product.name} (${product.category_name}) - ¥${product.price}`);
      });
    } catch (error) {
      console.log(`  查询失败: ${error.message}`);
    }
    
    // 测试订单信息查询
    console.log('\n🛒 订单信息示例:');
    try {
      const orders = await db.all(`
        SELECT 
          o.订单编号 as order_number,
          o.总金额 as total_amount,
          o.状态 as status,
          c.姓名 as customer_name
        FROM 销售订单 o
        LEFT JOIN 客户信息 c ON o.客户编号 = c.编号
        LIMIT 3
      `);
      
      if (orders.length > 0) {
        orders.forEach(order => {
          console.log(`  - ${order.order_number}: ¥${order.total_amount} (${order.status}) - ${order.customer_name || '散客'}`);
        });
      } else {
        console.log('  暂无订单数据');
      }
    } catch (error) {
      console.log(`  查询失败: ${error.message}`);
    }
    
    await db.close();
    console.log('\n✅ 数据库测试完成');
    
  } catch (error) {
    console.error('❌ 数据库测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testChineseDatabase();
