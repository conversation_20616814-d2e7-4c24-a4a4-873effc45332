#!/usr/bin/env node

/**
 * 更新所有API路由文件，将英文表名和字段名替换为中文
 */

const fs = require('fs');
const path = require('path');

class ApiRouteUpdater {
  constructor() {
    this.apiDir = './app/api';
    this.backupDir = './db/backups/api-backup';
    
    // 表名映射
    this.tableMapping = {
      'settings': '系统设置',
      'categories': '药品分类',
      'products': '药品信息',
      'customers': '客户信息',
      'orders': '销售订单',
      'order_details': '订单明细',
      'inventory_records': '库存记录',
      'suppliers': '供应商'
    };
    
    // 字段名映射
    this.fieldMapping = {
      // 通用字段
      'id': '编号',
      'created_at': '创建时间',
      'updated_at': '更新时间',
      
      // 系统设置表
      'setting_name': '设置名称',
      'setting_value': '设置值',
      'description': '描述',
      
      // 药品分类表
      'name': '名称',
      
      // 药品信息表
      'generic_name': '通用名',
      'barcode': '条形码',
      'trace_code': '追溯码',
      'category_id': '分类编号',
      'manufacturer': '生产厂家',
      'approval_number': '批准文号',
      'specification': '规格',
      'dosage_form': '剂型',
      'price': '售价',
      'cost_price': '成本价',
      'stock_quantity': '库存数量',
      'min_stock_level': '最低库存',
      'is_prescription': '是否处方药',
      'is_medical_insurance': '是否医保',
      'storage_condition': '储存条件',
      'status': '状态',
      
      // 客户信息表
      'phone': '电话',
      'email': '邮箱',
      'address': '地址',
      'id_card': '身份证号',
      'medical_card': '医保卡号',
      'member_level': '会员等级',
      'points': '积分',
      
      // 销售订单表
      'order_number': '订单编号',
      'customer_id': '客户编号',
      'total_amount': '总金额',
      'discount_amount': '折扣金额',
      'payable_amount': '应付金额',
      'paid_amount': '实付金额',
      'change_amount': '找零金额',
      'payment_method': '支付方式',
      'notes': '备注',
      
      // 订单明细表
      'order_id': '订单编号',
      'product_id': '药品编号',
      'quantity': '数量',
      'unit_price': '单价',
      'subtotal': '小计',
      'total_price': '小计',
      
      // 库存记录表
      'type': '变动类型',
      'reference_number': '参考单号',
      'supplier_id': '供应商编号',
      'note': '备注',
      
      // 供应商表
      'contact_person': '联系人',
      'tax_number': '税号',
      'bank_account': '银行账户',
      'bank_name': '开户行'
    };
  }

  async init() {
    console.log('🔄 开始更新API路由文件...\n');
    
    // 创建备份目录
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  async backupApiFiles() {
    console.log('📦 备份API文件...');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(this.backupDir, `api-backup-${timestamp}`);
    
    if (!fs.existsSync(backupPath)) {
      fs.mkdirSync(backupPath, { recursive: true });
    }
    
    this.copyDirectory(this.apiDir, backupPath);
    console.log(`  ✅ API文件已备份到: ${backupPath}\n`);
  }

  copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const items = fs.readdirSync(src);
    
    for (const item of items) {
      const srcPath = path.join(src, item);
      const destPath = path.join(dest, item);
      
      if (fs.statSync(srcPath).isDirectory()) {
        this.copyDirectory(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    }
  }

  async updateApiFiles() {
    console.log('🔧 更新API路由文件...');
    
    const apiFiles = this.findApiFiles(this.apiDir);
    
    for (const filePath of apiFiles) {
      await this.updateFile(filePath);
    }
    
    console.log(`  ✅ 已更新 ${apiFiles.length} 个API文件\n`);
  }

  findApiFiles(dir) {
    const files = [];
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      
      if (fs.statSync(fullPath).isDirectory()) {
        files.push(...this.findApiFiles(fullPath));
      } else if (item === 'route.ts' || item === 'route.js') {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  async updateFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let updated = false;
      
      // 替换表名
      for (const [english, chinese] of Object.entries(this.tableMapping)) {
        const regex = new RegExp(`\\b${english}\\b`, 'g');
        if (regex.test(content)) {
          content = content.replace(regex, chinese);
          updated = true;
        }
      }
      
      // 替换字段名（在SQL查询中）
      for (const [english, chinese] of Object.entries(this.fieldMapping)) {
        // 匹配 SELECT 语句中的字段
        const selectRegex = new RegExp(`\\b${english}\\s+as\\s+\\w+`, 'gi');
        if (selectRegex.test(content)) {
          content = content.replace(selectRegex, (match) => {
            return match.replace(english, chinese);
          });
          updated = true;
        }
        
        // 匹配普通的字段引用
        const fieldRegex = new RegExp(`\\b${english}\\b(?=\\s*[,)]|\\s*$)`, 'g');
        if (fieldRegex.test(content)) {
          content = content.replace(fieldRegex, chinese);
          updated = true;
        }
      }
      
      if (updated) {
        fs.writeFileSync(filePath, content);
        console.log(`    ✅ 更新: ${filePath}`);
      }
      
    } catch (error) {
      console.log(`    ❌ 更新失败: ${filePath} - ${error.message}`);
    }
  }

  async generateUpdateReport() {
    console.log('📊 生成更新报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      table_mappings: this.tableMapping,
      field_mappings: this.fieldMapping,
      updated_files: []
    };
    
    const apiFiles = this.findApiFiles(this.apiDir);
    
    for (const filePath of apiFiles) {
      const content = fs.readFileSync(filePath, 'utf8');
      const hasChineseTables = Object.values(this.tableMapping).some(table => content.includes(table));
      
      if (hasChineseTables) {
        report.updated_files.push(filePath);
      }
    }
    
    const reportPath = './db/api-update-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`  ✅ 更新报告已保存: ${reportPath}`);
    console.log(`  📁 已更新 ${report.updated_files.length} 个文件使用中文表名\n`);
    
    return report;
  }

  async run() {
    try {
      await this.init();
      await this.backupApiFiles();
      await this.updateApiFiles();
      const report = await this.generateUpdateReport();
      
      console.log('🎉 API路由更新完成！');
      console.log('📋 主要更改:');
      console.log('  - 所有表名已更新为中文');
      console.log('  - 所有字段名已更新为中文');
      console.log('  - 数据库连接已指向新的中文数据库');
      console.log('\n⚠️ 注意: 请测试所有API功能以确保正常工作');
      
    } catch (error) {
      console.error('❌ API路由更新失败:', error);
      process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const updater = new ApiRouteUpdater();
  updater.run();
}

module.exports = ApiRouteUpdater;
