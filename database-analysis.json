{"timestamp": "2025-06-08T14:36:21.958Z", "databases": {"files": [{"path": "./database.db", "exists": true, "size": 49152, "tables": [{"name": "settings", "records": 6}, {"name": "sqlite_sequence", "records": 6}, {"name": "categories", "records": 5}, {"name": "products", "records": 5}, {"name": "customers", "records": 5}, {"name": "orders", "records": 1}, {"name": "order_details", "records": 2}, {"name": "inventory_records", "records": 0}, {"name": "suppliers", "records": 0}], "totalRecords": 30, "lastModified": "2025-06-08T09:46:37.725Z"}, {"path": "./database.sqlite", "exists": true, "size": 77824, "tables": [{"name": "categories", "records": 11}, {"name": "sqlite_sequence", "records": 8}, {"name": "suppliers", "records": 0}, {"name": "inventory_records", "records": 1}, {"name": "operation_logs", "records": 0}, {"name": "customers", "records": 4}, {"name": "orders", "records": 7}, {"name": "product_batches", "records": 4}, {"name": "order_details", "records": 9}, {"name": "settings", "records": 6}, {"name": "products", "records": 4}], "totalRecords": 54, "lastModified": "2025-05-25T14:12:20.662Z"}, {"path": "./db/retail.db", "exists": true, "size": 180224, "tables": [{"name": "角色", "records": 3}, {"name": "sqlite_sequence", "records": 7}, {"name": "用户角色映射", "records": 1}, {"name": "用户", "records": 1}, {"name": "药品附加信息", "records": 0}, {"name": "操作日志", "records": 0}, {"name": "药品分类", "records": 8}, {"name": "药品", "records": 4}, {"name": "药品批次", "records": 4}, {"name": "客户", "records": 0}, {"name": "处方", "records": 0}, {"name": "处方明细", "records": 0}, {"name": "订单", "records": 0}, {"name": "订单明细", "records": 0}, {"name": "库存变动记录", "records": 1}, {"name": "供应商", "records": 2}, {"name": "采购订单", "records": 0}, {"name": "采购订单明细", "records": 0}], "totalRecords": 31, "lastModified": "2025-05-02T15:27:04.270Z"}], "activeDatabase": {"path": "./database.sqlite", "exists": true, "size": 77824, "tables": [{"name": "categories", "records": 11}, {"name": "sqlite_sequence", "records": 8}, {"name": "suppliers", "records": 0}, {"name": "inventory_records", "records": 1}, {"name": "operation_logs", "records": 0}, {"name": "customers", "records": 4}, {"name": "orders", "records": 7}, {"name": "product_batches", "records": 4}, {"name": "order_details", "records": 9}, {"name": "settings", "records": 6}, {"name": "products", "records": 4}], "totalRecords": 54, "lastModified": "2025-05-25T14:12:20.662Z"}, "recommendations": ["发现多个数据库文件，建议统一到db目录", "当前活跃数据库: ./database.sqlite"]}, "scripts": [{"path": "scripts\\add-mashangfangxin-settings.js", "name": "add-mashangfangxin-settings.js", "size": 4708, "type": ".js"}, {"path": "scripts\\add-payment-fields.js", "name": "add-payment-fields.js", "size": 3373, "type": ".js"}, {"path": "scripts\\check-categories-table.js", "name": "check-categories-table.js", "size": 1418, "type": ".js"}, {"path": "scripts\\check-db-tables.js", "name": "check-db-tables.js", "size": 1201, "type": ".js"}, {"path": "scripts\\check-db.js", "name": "check-db.js", "size": 783, "type": ".js"}, {"path": "scripts\\check-products-table.js", "name": "check-products-table.js", "size": 1418, "type": ".js"}, {"path": "scripts\\create-categories-table.js", "name": "create-categories-table.js", "size": 2411, "type": ".js"}, {"path": "scripts\\create-full-database.js", "name": "create-full-database.js", "size": 8234, "type": ".js"}, {"path": "scripts\\create-inventory-records-table.js", "name": "create-inventory-records-table.js", "size": 2043, "type": ".js"}, {"path": "scripts\\create-missing-tables.js", "name": "create-missing-tables.js", "size": 5564, "type": ".js"}, {"path": "scripts\\create-more-test-orders.js", "name": "create-more-test-orders.js", "size": 5205, "type": ".js"}, {"path": "scripts\\create-test-orders.js", "name": "create-test-orders.js", "size": 3863, "type": ".js"}, {"path": "scripts\\fix-database-data.js", "name": "fix-database-data.js", "size": 8747, "type": ".js"}, {"path": "scripts\\fix-inventory-records-table.js", "name": "fix-inventory-records-table.js", "size": 5674, "type": ".js"}, {"path": "scripts\\init-db.js", "name": "init-db.js", "size": 9874, "type": ".js"}, {"path": "scripts\\init-db.ts", "name": "init-db.ts", "size": 428, "type": ".ts"}, {"path": "scripts\\insert-categories-data.js", "name": "insert-categories-data.js", "size": 3061, "type": ".js"}, {"path": "scripts\\modify-products-table.js", "name": "modify-products-table.js", "size": 6091, "type": ".js"}, {"path": "scripts\\test-db-connection.js", "name": "test-db-connection.js", "size": 1160, "type": ".js"}, {"path": "scripts\\update-products-table.js", "name": "update-products-table.js", "size": 5986, "type": ".js"}, {"path": "scripts\\update-table-structure.js", "name": "update-table-structure.js", "size": 2598, "type": ".js"}, {"path": "app\\db\\connection.ts", "name": "connection.ts", "size": 443, "type": ".ts"}, {"path": "app\\db\\init.ts", "name": "init.ts", "size": 2845, "type": ".ts"}, {"path": "db\\db-manager.js", "name": "db-manager.js", "size": 8060, "type": ".js"}, {"path": "db\\migrate-to-unified-db.js", "name": "migrate-to-unified-db.js", "size": 8567, "type": ".js"}, {"path": "db\\schema.sql", "name": "schema.sql", "size": 21436, "type": ".sql"}, {"path": "db\\suppliers.sql", "name": "suppliers.sql", "size": 2545, "type": ".sql"}, {"path": "db\\中文数据库架构.sql", "name": "中文数据库架构.sql", "size": 8972, "type": ".sql"}]}