import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

// 获取药品列表
export async function GET() {
  try {
    const 药品信息 = await query(
      `SELECT
        p.编号 as id,
        p.名称 as name,
        p.通用名 as generic_name,
        p.描述 as description,
        p.条形码 as barcode,
        p.追溯码 as trace_code,
        p.分类编号 as category_id,
        c.名称 as category_name,
        p.生产厂家 as manufacturer,
        p.批准文号 as approval_number,
        p.规格 as specification,
        p.剂型 as dosage_form,
        p.售价 as price,
        p.成本价 as cost_price,
        p.库存数量 as stock_quantity,
        p.最低库存 as min_stock_level,
        p.是否处方药 as is_prescription,
        p.是否医保 as is_medical_insurance,
        p.储存条件 as storage_condition,
        p.状态 as status
      FROM 药品信息 p
      LEFT JOIN 药品分类 c ON p.分类编号 = c.编号
      ORDER BY p.编号 DESC`
    );

    // 处理布尔值转换
    const processedProducts = 药品信息.map(product => ({
      ...product,
      is_prescription: product.is_prescription === 1,
      is_medical_insurance: product.is_medical_insurance === 1
    }));

    return NextResponse.json({
      success: true,
      data: processedProducts
    });
  } catch (error) {
    console.error('获取药品列表失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取药品列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 创建新药品
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    console.log('POST接收到的数据:', data);

    const {
      name,
      generic_name,
      description,
      barcode,
      trace_code,
      category_id,
      manufacturer,
      approval_number,
      specification,
      dosage_form,
      price,
      cost_price,
      stock_quantity,
      min_stock_level,
      is_prescription,
      is_medical_insurance,
      storage_condition,
      status
    } = data;

    // 验证必填字段
    const missingFields = [];
    if (!name) missingFields.push('药品名称');
    if (!category_id) missingFields.push('药品分类');
    if (!manufacturer) missingFields.push('生产厂家');
    if (!specification) missingFields.push('规格');
    if (!dosage_form) missingFields.push('剂型');
    if (price === undefined || price === null) missingFields.push('销售价格');

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          message: '缺少必填字段',
          error: `请填写以下必填项: ${missingFields.join(', ')}`
        },
        { status: 400 }
      );
    }

    // 确保category_id是数字
    const categoryIdNum = typeof category_id === 'string' ? parseInt(category_id, 10) : category_id;

    // 检查分类是否存在
    const categoryCheck = await query('SELECT 编号 FROM 药品分类 WHERE 编号 = ?', [categoryIdNum]);
    if (!categoryCheck || categoryCheck.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '选择的药品分类不存在',
          error: `分类ID ${categoryIdNum} 不存在`
        },
        { status: 400 }
      );
    }

    // 检查条形码是否已存在（如果提供了条形码）
    if (barcode && barcode.trim() !== '') {
      const barcodeCheck = await query('SELECT 编号, 名称 FROM 药品信息 WHERE 条形码 = ?', [barcode.trim()]);
      if (barcodeCheck && barcodeCheck.length > 0) {
        return NextResponse.json(
          {
            success: false,
            message: '条形码已存在',
            error: `条形码 "${barcode}" 已被药品 "${barcodeCheck[0].名称}" (ID: ${barcodeCheck[0].编号}) 使用`
          },
          { status: 400 }
        );
      }
    }

    console.log('执行插入操作，参数:', [
      name, generic_name, description, barcode, trace_code, categoryIdNum,
      manufacturer, approval_number, specification, dosage_form,
      price, cost_price, stock_quantity, min_stock_level,
      is_prescription ? 1 : 0, is_medical_insurance ? 1 : 0, storage_condition, status
    ]);

    const result = await run(
      `INSERT INTO 药品信息 (
        名称, 通用名, 描述, 条形码, 追溯码, 分类编号,
        生产厂家, 批准文号, 规格, 剂型,
        售价, 成本价, 库存数量, 最低库存,
        是否处方药, 是否医保, 储存条件, 状态
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        name, generic_name, description, barcode, trace_code, categoryIdNum,
        manufacturer, approval_number, specification, dosage_form,
        price, cost_price, stock_quantity, min_stock_level,
        is_prescription ? 1 : 0, is_medical_insurance ? 1 : 0, storage_condition, status
      ]
    );

    console.log('插入结果:', result);

    if (!result.lastID) {
      return NextResponse.json(
        {
          success: false,
          message: '创建药品失败',
          error: '数据库操作未返回新记录ID'
        },
        { status: 500 }
      );
    }

    // 记录库存变动
    if (stock_quantity > 0) {
      const today = new Date().toISOString().split('T')[0]; // 获取当前日期，格式为YYYY-MM-DD

      try {
        // 创建一个批次记录
        const batchResult = await run(
          `INSERT INTO product_batches (
            product_id, batch_number, production_date, expiry_date, quantity, supplier_id, cost_price
          ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [
            result.lastID,
            `INIT-${result.lastID}`, // 初始批次号
            today, // 当前日期作为生产日期
            new Date(new Date().setFullYear(new Date().getFullYear() + 2)).toISOString().split('T')[0], // 2年后作为有效期
            stock_quantity,
            1, // 默认供应商ID，实际应用中可能需要调整
            cost_price || 0
          ]
        );

        console.log('批次创建结果:', batchResult);

        // 记录库存变动
        await run(
          `INSERT INTO 库存记录 (
            product_id, batch_id, quantity_change, change_type, note, operator_id
          ) VALUES (?, ?, ?, ?, ?, ?)`,
          [
            result.lastID,
            batchResult.lastID,
            stock_quantity,
            'adjustment',
            '初始库存录入',
            1 // 默认操作员ID
          ]
        );
      } catch (batchError) {
        console.error('创建批次或库存记录失败:', batchError);
        // 继续执行，不影响药品创建的成功状态
      }
    }

    const newProduct = await query(
      `SELECT
        编号 as id,
        名称 as name,
        通用名 as generic_name,
        描述 as description,
        条形码 as barcode,
        追溯码 as trace_code,
        分类编号 as category_id,
        生产厂家 as manufacturer,
        批准文号 as approval_number,
        规格 as specification,
        剂型 as dosage_form,
        售价 as price,
        成本价 as cost_price,
        库存数量 as stock_quantity,
        最低库存 as min_stock_level,
        是否处方药 as is_prescription,
        是否医保 as is_medical_insurance,
        储存条件 as storage_condition,
        状态 as status
      FROM 药品信息 WHERE 编号 = ?`,
      [result.lastID]
    );

    if (!newProduct || newProduct.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '创建药品后无法查询到新药品',
          error: `无法查询ID为 ${result.lastID} 的药品`
        },
        { status: 500 }
      );
    }

    // 处理布尔值转换
    const processedProduct = {
      ...newProduct[0],
      is_prescription: newProduct[0].is_prescription === 1,
      is_medical_insurance: newProduct[0].is_medical_insurance === 1
    };

    return NextResponse.json({
      success: true,
      data: processedProduct
    });
  } catch (error) {
    console.error('创建药品失败:', error);

    // 处理特定的数据库约束错误
    if (error instanceof Error) {
      if (error.message.includes('UNIQUE constraint failed: 药品信息.条形码')) {
        return NextResponse.json(
          {
            success: false,
            message: '条形码已存在',
            error: '该条形码已被其他药品使用，请检查条形码是否正确或使用其他条形码'
          },
          { status: 400 }
        );
      }

      if (error.message.includes('UNIQUE constraint failed')) {
        return NextResponse.json(
          {
            success: false,
            message: '数据重复',
            error: '提交的数据中包含重复的信息，请检查后重试'
          },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        message: '创建药品失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 更新药品信息
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json();
    console.log('PUT接收到的数据:', data);

    const {
      id,
      name,
      generic_name,
      description,
      barcode,
      trace_code,
      category_id,
      manufacturer,
      approval_number,
      specification,
      dosage_form,
      price,
      cost_price,
      min_stock_level,
      is_prescription,
      is_medical_insurance,
      storage_condition,
      status
    } = data;

    // 验证必填字段
    if (!id || !name || !category_id) {
      return NextResponse.json(
        {
          success: false,
          message: '缺少必填字段',
          error: `药品ID、名称和分类是必填项`
        },
        { status: 400 }
      );
    }

    // 确保category_id是数字
    const categoryIdNum = typeof category_id === 'string' ? parseInt(category_id, 10) : category_id;

    // 检查药品是否存在
    const productCheck = await query('SELECT 编号 as id FROM 药品信息 WHERE 编号 = ?', [id]);
    if (!productCheck || productCheck.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '药品不存在',
          error: `ID为 ${id} 的药品不存在`
        },
        { status: 404 }
      );
    }

    // 检查分类是否存在
    const categoryCheck = await query('SELECT 编号 as id FROM 药品分类 WHERE 编号 = ?', [categoryIdNum]);
    if (!categoryCheck || categoryCheck.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '选择的药品分类不存在',
          error: `分类ID ${categoryIdNum} 不存在`
        },
        { status: 400 }
      );
    }

    // 检查条形码是否已被其他药品使用（如果提供了条形码）
    if (barcode && barcode.trim() !== '') {
      const barcodeCheck = await query('SELECT 编号, 名称 FROM 药品信息 WHERE 条形码 = ? AND 编号 != ?', [barcode.trim(), id]);
      if (barcodeCheck && barcodeCheck.length > 0) {
        return NextResponse.json(
          {
            success: false,
            message: '条形码已存在',
            error: `条形码 "${barcode}" 已被药品 "${barcodeCheck[0].名称}" (ID: ${barcodeCheck[0].编号}) 使用`
          },
          { status: 400 }
        );
      }
    }

    console.log('执行更新操作，参数:', [
      name, generic_name, description, barcode, trace_code, categoryIdNum,
      manufacturer, approval_number, specification, dosage_form,
      price, cost_price, min_stock_level,
      is_prescription ? 1 : 0, is_medical_insurance ? 1 : 0, storage_condition, status, id
    ]);

    const result = await run(
      `UPDATE 药品信息
      SET
        名称 = ?,
        通用名 = ?,
        描述 = ?,
        条形码 = ?,
        追溯码 = ?,
        分类编号 = ?,
        生产厂家 = ?,
        批准文号 = ?,
        规格 = ?,
        剂型 = ?,
        售价 = ?,
        成本价 = ?,
        最低库存 = ?,
        是否处方药 = ?,
        是否医保 = ?,
        储存条件 = ?,
        状态 = ?,
        更新时间 = CURRENT_TIMESTAMP
      WHERE 编号 = ?`,
      [
        name, generic_name, description, barcode, trace_code, categoryIdNum,
        manufacturer, approval_number, specification, dosage_form,
        price, cost_price, min_stock_level,
        is_prescription ? 1 : 0, is_medical_insurance ? 1 : 0, storage_condition, status, id
      ]
    );

    console.log('更新结果:', result);

    const updatedProduct = await query(
      `SELECT
        编号 as id,
        名称 as name,
        通用名 as generic_name,
        描述 as description,
        条形码 as barcode,
        追溯码 as trace_code,
        分类编号 as category_id,
        生产厂家 as manufacturer,
        批准文号 as approval_number,
        规格 as specification,
        剂型 as dosage_form,
        售价 as price,
        成本价 as cost_price,
        库存数量 as stock_quantity,
        最低库存 as min_stock_level,
        是否处方药 as is_prescription,
        是否医保 as is_medical_insurance,
        储存条件 as storage_condition,
        状态 as status
      FROM 药品信息 WHERE 编号 = ?`,
      [id]
    );

    if (!updatedProduct || updatedProduct.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '更新后无法查询到药品',
          error: `无法查询ID为 ${id} 的药品`
        },
        { status: 404 }
      );
    }

    // 处理布尔值转换
    const processedProduct = {
      ...updatedProduct[0],
      is_prescription: updatedProduct[0].is_prescription === 1,
      is_medical_insurance: updatedProduct[0].is_medical_insurance === 1
    };

    return NextResponse.json({
      success: true,
      data: processedProduct
    });
  } catch (error) {
    console.error('更新药品失败:', error);

    // 处理特定的数据库约束错误
    if (error instanceof Error) {
      if (error.message.includes('UNIQUE constraint failed: 药品信息.条形码')) {
        return NextResponse.json(
          {
            success: false,
            message: '条形码已存在',
            error: '该条形码已被其他药品使用，请检查条形码是否正确或使用其他条形码'
          },
          { status: 400 }
        );
      }

      if (error.message.includes('UNIQUE constraint failed')) {
        return NextResponse.json(
          {
            success: false,
            message: '数据重复',
            error: '提交的数据中包含重复的信息，请检查后重试'
          },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        message: '更新药品失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 删除药品
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, message: '药品ID不能为空' },
        { status: 400 }
      );
    }

    // 检查药品是否存在
    const checkResult = await query(
      'SELECT 编号 as id FROM 药品信息 WHERE 编号 = ?',
      [id]
    );

    if (!checkResult || checkResult.length === 0) {
      return NextResponse.json(
        { success: false, message: '药品不存在' },
        { status: 404 }
      );
    }

    // 检查是否有关联的订单明细
    const orderDetails = await query('SELECT 编号 as id FROM 订单明细 WHERE product_id = ? LIMIT 1', [id]);
    if (orderDetails && orderDetails.length > 0) {
      // 如果有关联的订单明细，则只将状态设置为inactive
      await run('UPDATE 药品信息 SET 状态 = "inactive", 更新时间 = CURRENT_TIMESTAMP WHERE 编号 = ?', [id]);
    } else {
      // 如果没有关联的订单明细，则可以删除药品
      await run('DELETE FROM product_batches WHERE product_id = ?', [id]);
      await run('DELETE FROM 库存记录 WHERE product_id = ?', [id]);
      await run('DELETE FROM 药品信息 WHERE 编号 = ?', [id]);
    }

    return NextResponse.json({
      success: true,
      message: '药品删除成功'
    });
  } catch (error) {
    console.error('删除药品失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '删除药品失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}