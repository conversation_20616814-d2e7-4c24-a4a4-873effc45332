#!/usr/bin/env node

/**
 * 创建中文数据库并迁移现有数据
 * 这是数据库重构的核心脚本
 */

const sqlite3 = require('sqlite3');
const { open } = require('sqlite');
const fs = require('fs');
const path = require('path');

class ChineseDatabaseCreator {
  constructor() {
    this.newDbPath = './db/药店管理系统.db';
    this.sourceDbPath = './database.db'; // 当前主数据库
    this.backupDbPath = './database.sqlite'; // 备用数据库
    this.schemaPath = './db/中文数据库架构.sql';
    this.backupDir = './db/backups';
  }

  async init() {
    console.log('🚀 开始数据库重构任务...\n');
    
    // 确保目录存在
    if (!fs.existsSync('./db')) {
      fs.mkdirSync('./db', { recursive: true });
    }
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  async backupExistingDatabases() {
    console.log('📦 备份现有数据库...');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const databases = [
      { src: './database.db', name: 'main' },
      { src: './database.sqlite', name: 'sqlite' },
      { src: './db/retail.db', name: 'retail' }
    ];

    for (const db of databases) {
      if (fs.existsSync(db.src)) {
        const backupPath = path.join(this.backupDir, `${db.name}-backup-${timestamp}.db`);
        fs.copyFileSync(db.src, backupPath);
        console.log(`  ✅ 备份 ${db.src} → ${backupPath}`);
      }
    }
    console.log('');
  }

  async createChineseDatabase() {
    console.log('🏗️ 创建中文数据库架构...');
    
    // 删除现有的中文数据库（如果存在）
    if (fs.existsSync(this.newDbPath)) {
      fs.unlinkSync(this.newDbPath);
      console.log('  🗑️ 删除现有中文数据库');
    }

    // 创建新数据库
    const db = await open({
      filename: this.newDbPath,
      driver: sqlite3.Database
    });

    // 执行中文架构脚本
    if (fs.existsSync(this.schemaPath)) {
      const schema = fs.readFileSync(this.schemaPath, 'utf8');
      await db.exec(schema);
      console.log('  ✅ 中文数据库架构创建完成');
    } else {
      throw new Error('未找到中文数据库架构文件');
    }

    await db.close();
    console.log('');
  }

  async migrateData() {
    console.log('📊 开始数据迁移...');
    
    // 打开源数据库和目标数据库
    const sourceDb = await open({
      filename: this.sourceDbPath,
      driver: sqlite3.Database
    });
    
    const targetDb = await open({
      filename: this.newDbPath,
      driver: sqlite3.Database
    });

    try {
      // 迁移系统设置
      await this.migrateSettings(sourceDb, targetDb);
      
      // 迁移药品分类
      await this.migrateCategories(sourceDb, targetDb);
      
      // 迁移客户信息
      await this.migrateCustomers(sourceDb, targetDb);
      
      // 迁移药品信息
      await this.migrateProducts(sourceDb, targetDb);
      
      // 迁移销售订单
      await this.migrateOrders(sourceDb, targetDb);
      
      // 迁移订单明细
      await this.migrateOrderDetails(sourceDb, targetDb);
      
      // 迁移库存记录
      await this.migrateInventoryRecords(sourceDb, targetDb);
      
      // 迁移供应商（如果有数据）
      await this.migrateSuppliers(sourceDb, targetDb);

    } finally {
      await sourceDb.close();
      await targetDb.close();
    }
    
    console.log('  ✅ 数据迁移完成\n');
  }

  async migrateSettings(sourceDb, targetDb) {
    console.log('  📋 迁移系统设置...');
    
    try {
      const settings = await sourceDb.all('SELECT * FROM settings');
      
      for (const setting of settings) {
        await targetDb.run(
          'INSERT OR REPLACE INTO 系统设置 (设置名称, 设置值, 描述) VALUES (?, ?, ?)',
          [setting.setting_name, setting.setting_value, setting.description]
        );
      }
      
      console.log(`    ✅ 迁移 ${settings.length} 条系统设置`);
    } catch (error) {
      console.log(`    ⚠️ 系统设置迁移失败: ${error.message}`);
    }
  }

  async migrateCategories(sourceDb, targetDb) {
    console.log('  🏷️ 迁移药品分类...');
    
    try {
      const categories = await sourceDb.all('SELECT * FROM categories');
      
      for (const category of categories) {
        await targetDb.run(
          'INSERT OR REPLACE INTO 药品分类 (编号, 名称, 描述, 创建时间, 更新时间) VALUES (?, ?, ?, ?, ?)',
          [category.id, category.name, category.description, category.created_at, category.updated_at]
        );
      }
      
      console.log(`    ✅ 迁移 ${categories.length} 条药品分类`);
    } catch (error) {
      console.log(`    ⚠️ 药品分类迁移失败: ${error.message}`);
    }
  }

  async migrateCustomers(sourceDb, targetDb) {
    console.log('  👥 迁移客户信息...');
    
    try {
      const customers = await sourceDb.all('SELECT * FROM customers');
      
      for (const customer of customers) {
        await targetDb.run(
          `INSERT OR REPLACE INTO 客户信息 (
            编号, 姓名, 电话, 邮箱, 地址, 身份证号, 医保卡号, 
            会员等级, 积分, 创建时间, 更新时间
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            customer.id, customer.name, customer.phone, customer.email,
            customer.address, customer.id_card, customer.medical_card,
            customer.member_level || 'regular', customer.points || 0,
            customer.created_at, customer.updated_at
          ]
        );
      }
      
      console.log(`    ✅ 迁移 ${customers.length} 条客户信息`);
    } catch (error) {
      console.log(`    ⚠️ 客户信息迁移失败: ${error.message}`);
    }
  }

  async migrateProducts(sourceDb, targetDb) {
    console.log('  💊 迁移药品信息...');
    
    try {
      const products = await sourceDb.all('SELECT * FROM products');
      
      for (const product of products) {
        await targetDb.run(
          `INSERT OR REPLACE INTO 药品信息 (
            编号, 名称, 通用名, 描述, 条形码, 追溯码, 分类编号, 生产厂家,
            批准文号, 规格, 剂型, 售价, 成本价, 库存数量, 最低库存,
            是否处方药, 是否医保, 储存条件, 状态, 创建时间, 更新时间
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            product.id, product.name, product.generic_name, product.description,
            product.barcode, product.trace_code, product.category_id, product.manufacturer,
            product.approval_number, product.specification, product.dosage_form,
            product.price, product.cost_price, product.stock_quantity, product.min_stock_level,
            product.is_prescription || 0, product.is_medical_insurance || 0,
            product.storage_condition, product.status || 'active',
            product.created_at, product.updated_at
          ]
        );
      }
      
      console.log(`    ✅ 迁移 ${products.length} 条药品信息`);
    } catch (error) {
      console.log(`    ⚠️ 药品信息迁移失败: ${error.message}`);
    }
  }

  async migrateOrders(sourceDb, targetDb) {
    console.log('  🛒 迁移销售订单...');
    
    try {
      const orders = await sourceDb.all('SELECT * FROM orders');
      
      for (const order of orders) {
        await targetDb.run(
          `INSERT OR REPLACE INTO 销售订单 (
            编号, 订单编号, 客户编号, 总金额, 折扣金额, 应付金额,
            实付金额, 找零金额, 支付方式, 状态, 备注, 创建时间, 更新时间
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            order.id, order.order_number, order.customer_id, order.total_amount,
            order.discount_amount || 0, order.payable_amount, order.paid_amount || 0,
            order.change_amount || 0, order.payment_method || 'cash',
            order.status || 'pending', order.notes, order.created_at, order.updated_at
          ]
        );
      }
      
      console.log(`    ✅ 迁移 ${orders.length} 条销售订单`);
    } catch (error) {
      console.log(`    ⚠️ 销售订单迁移失败: ${error.message}`);
    }
  }

  async migrateOrderDetails(sourceDb, targetDb) {
    console.log('  📝 迁移订单明细...');

    try {
      const orderDetails = await sourceDb.all('SELECT * FROM order_details');

      for (const detail of orderDetails) {
        await targetDb.run(
          `INSERT OR REPLACE INTO 订单明细 (
            编号, 订单编号, 药品编号, 数量, 单价, 小计, 创建时间
          ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [
            detail.id, detail.order_id, detail.product_id,
            detail.quantity, detail.unit_price, detail.subtotal || (detail.quantity * detail.unit_price),
            detail.created_at || new Date().toISOString()
          ]
        );
      }

      console.log(`    ✅ 迁移 ${orderDetails.length} 条订单明细`);
    } catch (error) {
      console.log(`    ⚠️ 订单明细迁移失败: ${error.message}`);
    }
  }

  async migrateInventoryRecords(sourceDb, targetDb) {
    console.log('  📦 迁移库存记录...');
    
    try {
      const records = await sourceDb.all('SELECT * FROM inventory_records');
      
      for (const record of records) {
        await targetDb.run(
          `INSERT OR REPLACE INTO 库存记录 (
            编号, 药品编号, 变动数量, 变动类型, 参考单号, 供应商编号, 备注, 创建时间
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            record.id, record.product_id, record.quantity, record.type,
            record.reference_number, record.supplier_id, record.note, record.created_at
          ]
        );
      }
      
      console.log(`    ✅ 迁移 ${records.length} 条库存记录`);
    } catch (error) {
      console.log(`    ⚠️ 库存记录迁移失败: ${error.message}`);
    }
  }

  async migrateSuppliers(sourceDb, targetDb) {
    console.log('  🏢 迁移供应商信息...');
    
    try {
      const suppliers = await sourceDb.all('SELECT * FROM suppliers');
      
      for (const supplier of suppliers) {
        await targetDb.run(
          `INSERT OR REPLACE INTO 供应商 (
            编号, 名称, 联系人, 电话, 邮箱, 地址, 税号, 银行账户, 开户行, 状态, 创建时间, 更新时间
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            supplier.id, supplier.name, supplier.contact_person, supplier.phone,
            supplier.email, supplier.address, supplier.tax_number, supplier.bank_account,
            supplier.bank_name, supplier.status || 'active', supplier.created_at, supplier.updated_at
          ]
        );
      }
      
      console.log(`    ✅ 迁移 ${suppliers.length} 条供应商信息`);
    } catch (error) {
      console.log(`    ⚠️ 供应商信息迁移失败: ${error.message}`);
    }
  }

  async generateReport() {
    console.log('📊 生成迁移报告...');
    
    const db = await open({
      filename: this.newDbPath,
      driver: sqlite3.Database
    });

    const tables = [
      { chinese: '系统设置', english: 'settings' },
      { chinese: '药品分类', english: 'categories' },
      { chinese: '客户信息', english: 'customers' },
      { chinese: '药品信息', english: 'products' },
      { chinese: '销售订单', english: 'orders' },
      { chinese: '订单明细', english: 'order_details' },
      { chinese: '库存记录', english: 'inventory_records' },
      { chinese: '供应商', english: 'suppliers' }
    ];

    const report = {
      timestamp: new Date().toISOString(),
      database: this.newDbPath,
      tables: []
    };

    for (const table of tables) {
      try {
        const result = await db.get(`SELECT COUNT(*) as count FROM ${table.chinese}`);
        report.tables.push({
          chinese_name: table.chinese,
          english_name: table.english,
          record_count: result.count
        });
        console.log(`  📋 ${table.chinese}: ${result.count} 条记录`);
      } catch (error) {
        console.log(`  ❌ ${table.chinese}: 查询失败`);
      }
    }

    await db.close();

    // 保存报告
    const reportPath = './db/migration-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`  ✅ 迁移报告已保存: ${reportPath}\n`);

    return report;
  }

  async run() {
    try {
      await this.init();
      await this.backupExistingDatabases();
      await this.createChineseDatabase();
      await this.migrateData();
      const report = await this.generateReport();
      
      console.log('🎉 数据库重构完成！');
      console.log(`📁 新数据库位置: ${this.newDbPath}`);
      console.log(`📊 总计迁移: ${report.tables.reduce((sum, t) => sum + t.record_count, 0)} 条记录`);
      console.log('\n下一步: 更新代码以使用新的中文数据库');
      
    } catch (error) {
      console.error('❌ 数据库重构失败:', error);
      process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const creator = new ChineseDatabaseCreator();
  creator.run();
}

module.exports = ChineseDatabaseCreator;
